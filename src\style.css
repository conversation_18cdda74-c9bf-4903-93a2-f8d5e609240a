* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    min-height: 100vh;
    background: transparent;
    overflow-x: hidden;
    position: relative;
    scroll-behavior: smooth;
}

#starCanvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
    background: #0a0a0a;
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@keyframes fadeOut {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

.content {
    position: relative;
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    color: white;
    text-align: center;
    font-family: 'Arial', sans-serif;
    user-select: none;
}

.second-page {
    background: transparent;
}

.third-page {
    background: transparent;
}

.section-title {
    font-size: 2.5rem;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.4);
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.9);
}

.section-text {
    font-size: 1.1rem;
    opacity: 0.8;
    margin-bottom: 1rem;
    max-width: 600px;
    line-height: 1.6;
}

.title {
    font-size: 6rem;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    margin-bottom: 1rem;
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.8;
}

/* 滚动指示器样式 */
.scroll-indicator {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.scroll-indicator:hover {
    transform: translateX(-50%) translateY(-5px);
}

.scroll-arrow span {
    display: block;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin-bottom: 10px;
    font-weight: 300;
    letter-spacing: 1px;
}

.arrow-down {
    position: relative;
    width: 2px;
    height: 30px;
    margin: 0 auto;
}



.arrow-head {
    position: absolute;
    bottom: 0;
    left: -3px;
    width: 8px;
    height: 8px;
    border-right: 2px solid rgba(255, 255, 255, 0.8);
    border-bottom: 2px solid rgba(255, 255, 255, 0.8);
    transform: rotate(45deg);
    animation: arrowBounce 2s infinite ease-in-out;
}

/* 向上箭头样式 */
.arrow-up {
    position: relative;
    width: 2px;
    height: 30px;
    margin: 0 auto;
}

.arrow-up .arrow-line {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 2px;
    height: 20px;
    background: linear-gradient(to top, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.8));
    animation: arrowPulseUp 2s infinite ease-in-out;
}

.arrow-head-up {
    position: absolute;
    top: 0;
    left: -3px;
    width: 8px;
    height: 8px;
    border-left: 2px solid rgba(255, 255, 255, 0.8);
    border-top: 2px solid rgba(255, 255, 255, 0.8);
    transform: rotate(45deg);
    animation: arrowBounceUp 2s infinite ease-in-out;
}

@keyframes arrowPulse {
    0%, 100% {
        opacity: 0.3;
        transform: scaleY(0.8) translateX(0);
    }
    50% {
        opacity: 1;
        transform: scaleY(1) translateX(2px);
    }
}

@keyframes arrowPulseUp {
    0%, 100% {
        opacity: 0.3;
        transform: scaleY(0.8) translateX(0);
    }
    50% {
        opacity: 1;
        transform: scaleY(1) translateX(-2px);
    }
}

@keyframes arrowBounce {
    0%, 100% {
        transform: rotate(45deg) translateX(0);
        opacity: 0.6;
    }
    50% {
        transform: rotate(45deg) translateX(3px);
        opacity: 1;
    }
}

@keyframes arrowBounceUp {
    0%, 100% {
        transform: rotate(45deg) translateX(0);
        opacity: 0.6;
    }
    50% {
        transform: rotate(45deg) translateX(-3px);
        opacity: 1;
    }
}

/* 背景音乐控制按钮 */
.music-control {
    position: fixed;
    top: 30px;
    right: 30px;
    z-index: 1000;
    cursor: pointer;
    transition: all 0.3s ease;
    /* 扩大悬停区域以包含音量控制条 */
    padding-bottom: 100px;
}

.music-control:hover .vinyl-record {
    transform: scale(1.1);
}

.music-control:hover .vinyl-record {
    border-color: rgba(255, 255, 255, 1);
    transform: scale(1.1);
}

.music-control:hover .vinyl-record::before {
    border-color: rgba(255, 255, 255, 0.8);
}

.music-control:hover .vinyl-record::after {
    border-color: rgba(255, 255, 255, 0.6);
}

.music-control:hover .vinyl-center {
    border-color: rgba(255, 255, 255, 1);
}

.vinyl-record {
    width: 40px;
    height: 40px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;
}

.vinyl-record.playing {
    animation: vinylSpin 3s linear infinite;
}

.vinyl-record::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 1px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
}

.vinyl-record::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
}

.vinyl-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    border: 1px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
}

.vinyl-hole {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2px;
    height: 2px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
}

/* 音符特效容器 */
.music-notes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100px;
    height: 100px;
    pointer-events: none;
    overflow: visible;
}

/* 音量控制条 */
.volume-control {
    position: absolute;
    top: 60px;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: none;
    padding: 10px; /* 增加悬停区域 */
}

.music-control:hover .volume-control {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

.volume-slider {
    width: 8px;
    height: 80px;
    position: relative;
    cursor: pointer;
}

.volume-track {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.6);
    border-radius: 4px;
}

.volume-fill {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 30%;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 0 0 3px 3px;
    transition: height 0.2s ease;
}

.volume-handle {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 12px;
    height: 4px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 2px;
    transition: all 0.2s ease;
    bottom: calc(30% - 2px);
}

.volume-handle:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateX(-50%) scale(1.2);
}

/* 音符样式 */
.music-note {
    position: absolute;
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    font-weight: bold;
    pointer-events: none;
    animation: noteFloat 3s ease-out forwards;
}

/* 音符浮动动画 */
@keyframes noteFloat {
    0% {
        opacity: 1;
        transform: translate(0, 0) scale(0.5);
    }
    50% {
        opacity: 0.8;
        transform: translate(var(--random-x), var(--random-y)) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(calc(var(--random-x) * 2), calc(var(--random-y) * 2)) scale(0.3);
    }
}

/* 唱片旋转动画 */
@keyframes vinylSpin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 星座连线效果 */
.constellation {
    position: absolute;
    stroke: rgba(255, 255, 255, 0.3);
    stroke-width: 1;
    fill: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .title {
        font-size: 2rem;
    }
    .subtitle {
        font-size: 1rem;
    }
    
    .scroll-indicator {
        bottom: 30px;
    }
    
    .scroll-arrow span {
        font-size: 12px;
    }
} 
