class CanvasStarryNight {
    constructor() {
        this.canvas = document.getElementById('starCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.stars = [];
        this.shootingStars = [];
        this.constellations = [
            // 第一页星座
            {
                name: "大熊座",
                page: 1,
                points: [
                    { x: 0.15, y: 0.25 },
                    { x: 0.18, y: 0.22 },
                    { x: 0.22, y: 0.20 },
                    { x: 0.26, y: 0.22 },
                    { x: 0.30, y: 0.28 },
                    { x: 0.28, y: 0.32 },
                    { x: 0.24, y: 0.35 }
                ]
            },
            {
                name: "猎户座",
                page: 1,
                points: [
                    { x: 0.65, y: 0.15 },
                    { x: 0.70, y: 0.18 },
                    { x: 0.75, y: 0.15 },
                    { x: 0.68, y: 0.25 },
                    { x: 0.72, y: 0.25 },
                    { x: 0.65, y: 0.35 },
                    { x: 0.70, y: 0.38 },
                    { x: 0.75, y: 0.35 }
                ]
            },
            // 第二页星座
            {
                name: "仙女座",
                page: 2,
                points: [
                    { x: 0.20, y: 0.20 },
                    { x: 0.25, y: 0.25 },
                    { x: 0.30, y: 0.22 },
                    { x: 0.35, y: 0.28 },
                    { x: 0.32, y: 0.35 }
                ]
            },
            {
                name: "天鹅座",
                page: 2,
                points: [
                    { x: 0.55, y: 0.45 },
                    { x: 0.60, y: 0.50 },
                    { x: 0.65, y: 0.48 },
                    { x: 0.70, y: 0.52 },
                    { x: 0.62, y: 0.58 },
                    { x: 0.58, y: 0.55 }
                ]
            },
            // 第三页星座
            {
                name: "小熊座",
                page: 3,
                points: [
                    { x: 0.12, y: 0.25 },
                    { x: 0.15, y: 0.28 },
                    { x: 0.18, y: 0.25 },
                    { x: 0.20, y: 0.30 },
                    { x: 0.17, y: 0.35 },
                    { x: 0.14, y: 0.32 },
                    { x: 0.11, y: 0.30 }
                ]
            },
            {
                name: "天琴座",
                page: 3,
                points: [
                    { x: 0.78, y: 0.15 },
                    { x: 0.80, y: 0.20 },
                    { x: 0.82, y: 0.18 },
                    { x: 0.84, y: 0.25 },
                    { x: 0.81, y: 0.30 }
                ]
            },
            {
                name: "飞马座",
                page: 3,
                points: [
                    { x: 0.45, y: 0.50 },
                    { x: 0.50, y: 0.48 },
                    { x: 0.55, y: 0.52 },
                    { x: 0.52, y: 0.58 },
                    { x: 0.48, y: 0.55 }
                ]
            }
        ];
        this.mouseX = 0;
        this.mouseY = 0;

        // 为每个星座添加动画状态跟踪
        this.constellations.forEach(constellation => {
            // 第一页的星座初始就应该可见
            constellation.currentAlpha = constellation.page === 1 ? 1 : 0;
            constellation.targetAlpha = constellation.page === 1 ? 1 : 0;
            constellation.lastUpdateTime = Date.now();
        });

        this.init();
    }

    init() {
        this.setupCanvas();
        this.createStars(200);
        this.createShootingStarTimer();
        this.setupEventListeners();
        this.animate();
    }

    setupCanvas() {
        const updateCanvasSize = () => {
            this.canvas.width = window.innerWidth;
            this.canvas.height = document.body.scrollHeight;
        };
        
        updateCanvasSize();
        window.addEventListener('resize', updateCanvasSize);
        // 页面内容变化时也更新canvas大小
        window.addEventListener('scroll', () => {
            if (this.canvas.height !== document.body.scrollHeight) {
                updateCanvasSize();
            }
        });
    }

    createStars(count) {
        this.stars = [];
        for (let i = 0; i < count; i++) {
            this.stars.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                size: Math.random() < 0.6 ? 1 : Math.random() < 0.9 ? 2 : 3,
                opacity: 0.3 + Math.random() * 0.7,
                twinkleSpeed: 0.01 + Math.random() * 0.02,
                twinklePhase: Math.random() * Math.PI * 2,
                baseOpacity: 0.3 + Math.random() * 0.4,
                breatheSpeed: 0.005 + Math.random() * 0.01,
                breathePhase: Math.random() * Math.PI * 2,
                breatheAmplitude: 0.2 + Math.random() * 0.3,
                isNearMouse: false
            });
        }
    }

    createShootingStarTimer() {
        setInterval(() => {
            if (Math.random() < 0.25) {
                this.createShootingStar();
            }
        }, 2500);
    }

    createShootingStar() {
        const viewportHeight = window.innerHeight;
        const scrollY = window.scrollY;

        const startX = -300 - Math.random() * 200; // 增加左侧范围：-300到-500
        const startY = scrollY + Math.random() * (viewportHeight * 0.4); // 增加起始高度范围：40%
        const endX = this.canvas.width + 300 + Math.random() * 200; // 增加右侧范围：+300到+500
        const endY = scrollY + viewportHeight * 0.6 + Math.random() * viewportHeight * 0.6; // 增加结束高度范围：60%到120%
        
        const duration = 4000 + Math.random() * 3000; // 4-7秒
        
        const colors = [
            'rgba(255, 255, 255, 1)',
            'rgba(255, 248, 220, 1)',
            'rgba(173, 216, 230, 1)',
            'rgba(255, 182, 193, 1)'
        ];
        
        this.shootingStars.push({
            startX,
            startY,
            endX,
            endY,
            currentX: startX,
            currentY: startY,
            progress: 0,
            duration,
            startTime: Date.now(),
            length: 60 + Math.random() * 120,
            color: colors[Math.floor(Math.random() * colors.length)],
            opacity: 0,
            fadeInDuration: 300,
            fadeOutDuration: 2000 + Math.random() * 1000
        });
    }

    drawStars() {
        this.stars.forEach(star => {
            // 更新闪烁效果
            star.twinklePhase += star.twinkleSpeed;
            const twinkleOpacity = star.baseOpacity + Math.sin(star.twinklePhase) * 0.3;

            // 更新呼吸效果
            star.breathePhase += star.breatheSpeed;
            const breatheScale = 1 + Math.sin(star.breathePhase) * star.breatheAmplitude;
            const breatheOpacity = 0.8 + Math.sin(star.breathePhase * 0.7) * 0.2;

            // 鼠标交互效果
            const distance = Math.sqrt(
                Math.pow(this.mouseX - star.x, 2) +
                Math.pow(this.mouseY - star.y, 2)
            );

            let finalOpacity = twinkleOpacity * breatheOpacity;
            let finalSize = star.size * breatheScale;
            
            if (distance < 100) {
                const factor = 1 - (distance / 100);
                finalOpacity = Math.min(1, twinkleOpacity + factor * 0.7);
                finalSize = star.size * (1 + factor * 0.5);
            }
            
            this.ctx.save();
            this.ctx.globalAlpha = finalOpacity;
            this.ctx.fillStyle = 'white';
            
            if (star.size >= 3) {
                // 大星星添加光晕效果
                this.ctx.shadowColor = 'rgba(255, 255, 255, 0.8)';
                this.ctx.shadowBlur = 6;
            }
            
            this.ctx.beginPath();
            this.ctx.arc(star.x, star.y, finalSize / 2, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();
        });
    }

    drawShootingStars() {
        const currentTime = Date.now();
        const scrollY = window.scrollY;
        const viewportHeight = window.innerHeight;

        this.shootingStars = this.shootingStars.filter(star => {
            const elapsed = currentTime - star.startTime;
            const progress = elapsed / star.duration;

            if (progress >= 1) return false; // 移除完成的流星

            // 计算位置
            star.currentX = star.startX + (star.endX - star.startX) * progress;
            star.currentY = star.startY + (star.endY - star.startY) * progress;

            // 只绘制在当前视口附近的流星
            const starViewportY = star.currentY - scrollY;
            if (starViewportY < -200 || starViewportY > viewportHeight + 200) {
                return true; // 保留流星但不绘制
            }

            // 计算透明度
            const fadeInEnd = star.fadeInDuration;
            const fadeOutStart = star.duration - star.fadeOutDuration;

            if (elapsed < fadeInEnd) {
                star.opacity = elapsed / fadeInEnd;
            } else if (elapsed > fadeOutStart) {
                star.opacity = (star.duration - elapsed) / star.fadeOutDuration;
            } else {
                star.opacity = 1;
            }

            // 绘制流星
            this.drawShootingStar(star);

            return true;
        });
    }

    drawShootingStar(star) {
        const angle = Math.atan2(star.endY - star.startY, star.endX - star.startX);
        
        this.ctx.save();
        this.ctx.globalAlpha = star.opacity;
        
        // 绘制主体
        this.ctx.translate(star.currentX, star.currentY);
        this.ctx.rotate(angle);
        
        // 创建渐变
        const gradient = this.ctx.createLinearGradient(-star.length, 0, 0, 0);
        gradient.addColorStop(0, 'rgba(255, 255, 255, 0)');
        gradient.addColorStop(0.3, 'rgba(255, 255, 255, 0.3)');
        gradient.addColorStop(1, star.color);
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(-star.length, -1, star.length, 2);
        
        // 添加光晕效果
        this.ctx.shadowColor = 'rgba(255, 255, 255, 0.8)';
        this.ctx.shadowBlur = 8;
        this.ctx.fillRect(-star.length * 0.3, -0.5, star.length * 0.3, 1);
        
        this.ctx.restore();
    }

    drawConstellation() {
        const scrollY = window.scrollY;
        const viewportHeight = window.innerHeight;

        this.ctx.save();

        // 绘制所有星座，根据其在视口中的位置计算透明度
        this.constellations.forEach(constellation => {
            // 计算星座所在页面的起始位置
            const pageStartY = (constellation.page - 1) * viewportHeight;

            // 计算星座页面相对于当前滚动位置的进度
            const pageProgress = (scrollY - pageStartY) / viewportHeight;

            // 扩大绘制范围，避免快速滚动时突然消失
            // 只有当星座完全不可见且已经完成过渡时才跳过
            if ((pageProgress < -1.0 || pageProgress > 2.0) && constellation.currentAlpha < 0.01) return;

            // 根据页面滚动进度计算目标透明度
            let targetAlpha = 0;

            // 统一的淡入淡出逻辑，支持双向滚动
            if (pageProgress < -0.5) {
                // 页面在上方，从上方淡入（向下滚动时）
                targetAlpha = Math.max(0, 1 + (pageProgress + 0.5) / 0.3);
            } else if (pageProgress < -0.2) {
                // 页面即将从上方进入视口
                targetAlpha = Math.max(0, 1 + (pageProgress + 0.2) / 0.3);
            } else if (pageProgress <= 0.8) {
                // 页面在视口中，完全可见
                targetAlpha = 1;
            } else if (pageProgress <= 1.2) {
                // 页面即将从下方离开视口，向下淡出
                targetAlpha = Math.max(0, 1 - (pageProgress - 0.8) / 0.4);
            } else if (pageProgress <= 1.5) {
                // 页面在下方，从下方淡入（向上滚动时）
                targetAlpha = Math.max(0, 1 - (pageProgress - 1.2) / 0.3);
            } else {
                // 页面完全离开视口
                targetAlpha = 0;
            }

            // 特殊处理：确保第一页在页面顶部时完全可见
            if (constellation.page === 1 && pageProgress <= 0) {
                targetAlpha = 1;
            }

            // 平滑过渡到目标透明度
            const currentTime = Date.now();
            const deltaTime = currentTime - constellation.lastUpdateTime;
            constellation.lastUpdateTime = currentTime;

            // 过渡速度（每秒变化的透明度）- 降低速度让过渡更明显
            const transitionSpeed = 1.5; // 降低过渡速度
            const maxChange = (transitionSpeed * deltaTime) / 1000;

            const alphaDiff = targetAlpha - constellation.currentAlpha;
            if (Math.abs(alphaDiff) <= maxChange) {
                constellation.currentAlpha = targetAlpha;
            } else {
                constellation.currentAlpha += Math.sign(alphaDiff) * maxChange;
            }

            // 确保透明度在有效范围内
            constellation.currentAlpha = Math.max(0, Math.min(1, constellation.currentAlpha));

            const alpha = constellation.currentAlpha;
            // 只有当透明度非常小时才跳过绘制
            if (alpha < 0.01) return;

            // 设置连线样式
            this.ctx.strokeStyle = `rgba(255, 255, 255, ${0.4 * alpha})`;
            this.ctx.lineWidth = 1.5;
            this.ctx.shadowColor = `rgba(255, 255, 255, ${0.6 * alpha})`;
            this.ctx.shadowBlur = 2;

            // 计算保持16:10宽高比的坐标系统
            const targetAspectRatio = 16 / 10;
            const currentAspectRatio = this.canvas.width / viewportHeight;

            let scaleX, scaleY, offsetX, offsetY;

            if (currentAspectRatio > targetAspectRatio) {
                // 窗口比目标更宽，以高度为准
                scaleY = viewportHeight;
                scaleX = viewportHeight * targetAspectRatio;
                offsetX = (this.canvas.width - scaleX) / 2;
                offsetY = 0;
            } else {
                // 窗口比目标更窄，以宽度为准
                scaleX = this.canvas.width;
                scaleY = this.canvas.width / targetAspectRatio;
                offsetX = 0;
                offsetY = (viewportHeight - scaleY) / 2;
            }

            // 绘制连线
            this.ctx.beginPath();
            constellation.points.forEach((point, index) => {
                const x = offsetX + point.x * scaleX;
                // 计算星座在对应页面的绝对Y坐标
                const y = (constellation.page - 1) * viewportHeight + offsetY + point.y * scaleY;

                if (index === 0) {
                    this.ctx.moveTo(x, y);
                } else {
                    this.ctx.lineTo(x, y);
                }
            });
            this.ctx.stroke();

            // 绘制星座点（星星）
            this.ctx.fillStyle = `rgba(255, 255, 255, ${alpha})`;
            this.ctx.shadowColor = `rgba(255, 255, 255, ${0.9 * alpha})`;
            this.ctx.shadowBlur = 8;

            constellation.points.forEach(point => {
                const x = offsetX + point.x * scaleX;
                // 计算星座在对应页面的绝对Y坐标
                const y = (constellation.page - 1) * viewportHeight + offsetY + point.y * scaleY;

                // 添加呼吸效果
                const time = Date.now() * 0.002;
                const breathe = 1 + Math.sin(time + x * 0.01 + y * 0.01) * 0.3;
                const radius = 2.5 * breathe;

                this.ctx.globalAlpha = alpha;
                this.ctx.beginPath();
                this.ctx.arc(x, y, radius, 0, Math.PI * 2);
                this.ctx.fill();

                // 添加额外的光晕
                this.ctx.globalAlpha = 0.3 * alpha;
                this.ctx.beginPath();
                this.ctx.arc(x, y, radius * 2, 0, Math.PI * 2);
                this.ctx.fill();
            });
        });

        this.ctx.restore();
    }

    setupEventListeners() {
        // 鼠标移动
        document.addEventListener('mousemove', (e) => {
            this.mouseX = e.clientX;
            this.mouseY = e.clientY + window.pageYOffset;
        });

        // 空格键增加星星
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space') {
                e.preventDefault();
                this.createStars(50);
            }
        });

        // 滚动指示器 - 第一页
        const scrollIndicator = document.getElementById('scrollIndicator');
        if (scrollIndicator) {
            scrollIndicator.addEventListener('click', () => {
                const windowHeight = window.innerHeight;
                window.scrollTo({
                    top: windowHeight,
                    behavior: 'smooth'
                });
            });
        }

        // 滚动指示器 - 第三页（返回顶部）
        const scrollIndicatorThird = document.getElementById('scrollIndicatorThird');
        if (scrollIndicatorThird) {
            scrollIndicatorThird.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // 点击事件检查更新
        document.addEventListener('click', (e) => {
            if (e.target.id === 'scrollIndicator' || e.target.closest('#scrollIndicator') ||
                e.target.id === 'scrollIndicatorThird' || e.target.closest('#scrollIndicatorThird')) return;
            
            for (let i = 0; i < 3; i++) {
                setTimeout(() => {
                    this.createShootingStar();
                }, i * 200);
            }
        });

        // 窗口大小变化时重新创建星星
        window.addEventListener('resize', () => {
            this.createStars(200);
        });
    }

    animate() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        this.drawStars();
        this.drawShootingStars();
        this.drawConstellation();
        
        requestAnimationFrame(() => this.animate());
    }
}

// 背景音乐控制类
class MusicController {
    constructor() {
        this.audio = null;
        this.isPlaying = false;
        this.musicControl = document.getElementById('musicControl');
        this.vinylRecord = this.musicControl.querySelector('.vinyl-record');
        this.musicNotes = document.getElementById('musicNotes');
        this.noteInterval = null;

        this.init();
    }

    init() {
        // 创建音频对象（这里使用一个示例音频URL，您可以替换为实际的音乐文件）
        this.audio = new Audio();
        this.audio.loop = true;
        this.audio.volume = 0.3;

        // 您可以在这里设置音乐文件的URL
        this.audio.src = '../res/audios/umbrella.mp3';

        // 添加点击事件
        this.musicControl.addEventListener('click', () => this.toggleMusic());

        // 音频加载完成事件
        this.audio.addEventListener('canplaythrough', () => {
            console.log('音频已加载完成');
        });

        // 音频播放错误事件
        this.audio.addEventListener('error', (e) => {
            console.log('音频加载失败:', e);
        });
    }

    toggleMusic() {
        if (this.isPlaying) {
            this.pauseMusic();
        } else {
            this.playMusic();
        }
    }

    playMusic() {
        if (this.audio.src) {
            this.audio.play().then(() => {
                this.isPlaying = true;
                this.vinylRecord.classList.add('playing');
                this.startNoteEffect();
                console.log('音乐开始播放');
            }).catch((error) => {
                console.log('播放失败:', error);
            });
        } else {
            console.log('请先设置音乐文件路径');
            // 如果没有音乐文件，仍然显示旋转动画作为演示
            this.isPlaying = true;
            this.vinylRecord.classList.add('playing');
            this.startNoteEffect();
        }
    }

    pauseMusic() {
        if (this.audio) {
            this.audio.pause();
        }
        this.isPlaying = false;
        this.vinylRecord.classList.remove('playing');
        this.stopNoteEffect();
        console.log('音乐已暂停');
    }

    startNoteEffect() {
        // 清除之前的定时器
        if (this.noteInterval) {
            clearInterval(this.noteInterval);
        }

        // 每800ms生成一个音符
        this.noteInterval = setInterval(() => {
            this.createMusicNote();
        }, 800);
    }

    stopNoteEffect() {
        if (this.noteInterval) {
            clearInterval(this.noteInterval);
            this.noteInterval = null;
        }

        // 清除现有的音符
        this.musicNotes.innerHTML = '';
    }

    createMusicNote() {
        const notes = ['♪', '♫', '♬', '♩', '♭', '♯'];
        const note = document.createElement('div');
        note.className = 'music-note';
        note.textContent = notes[Math.floor(Math.random() * notes.length)];

        // 随机位置（从唱片周围开始）
        const angle = Math.random() * 2 * Math.PI;
        const radius = 25 + Math.random() * 10; // 从唱片边缘开始
        const startX = Math.cos(angle) * radius;
        const startY = Math.sin(angle) * radius;

        // 随机移动方向
        const randomX = (Math.random() - 0.5) * 60;
        const randomY = (Math.random() - 0.5) * 60 - 30; // 偏向上方

        note.style.left = `${50 + startX}px`;
        note.style.top = `${50 + startY}px`;
        note.style.setProperty('--random-x', `${randomX}px`);
        note.style.setProperty('--random-y', `${randomY}px`);

        this.musicNotes.appendChild(note);

        // 3秒后移除音符
        setTimeout(() => {
            if (note.parentNode) {
                note.parentNode.removeChild(note);
            }
        }, 3000);
    }

    setMusicSource(src) {
        this.audio.src = src;
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new CanvasStarryNight();
    new MusicController();
});