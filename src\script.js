class CanvasStarryNight {
    constructor() {
        this.canvas = document.getElementById('starCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.stars = [];
        this.shootingStars = [];
        this.constellations = [
            // 第一页星座
            {
                name: "大熊座",
                page: 1,
                points: [
                    { x: 0.15, y: 0.25 },
                    { x: 0.18, y: 0.22 },
                    { x: 0.22, y: 0.20 },
                    { x: 0.26, y: 0.22 },
                    { x: 0.30, y: 0.28 },
                    { x: 0.28, y: 0.32 },
                    { x: 0.24, y: 0.35 }
                ]
            },
            {
                name: "猎户座",
                page: 1,
                points: [
                    { x: 0.65, y: 0.15 },
                    { x: 0.70, y: 0.18 },
                    { x: 0.75, y: 0.15 },
                    { x: 0.68, y: 0.25 },
                    { x: 0.72, y: 0.25 },
                    { x: 0.65, y: 0.35 },
                    { x: 0.70, y: 0.38 },
                    { x: 0.75, y: 0.35 }
                ]
            },
            // 第二页星座
            {
                name: "仙女座",
                page: 2,
                points: [
                    { x: 0.20, y: 0.20 },
                    { x: 0.25, y: 0.25 },
                    { x: 0.30, y: 0.22 },
                    { x: 0.35, y: 0.28 },
                    { x: 0.32, y: 0.35 }
                ]
            },
            {
                name: "天鹅座",
                page: 2,
                points: [
                    { x: 0.55, y: 0.45 },
                    { x: 0.60, y: 0.50 },
                    { x: 0.65, y: 0.48 },
                    { x: 0.70, y: 0.52 },
                    { x: 0.62, y: 0.58 },
                    { x: 0.58, y: 0.55 }
                ]
            },
            // 第三页星座
            {
                name: "小熊座",
                page: 3,
                points: [
                    { x: 0.12, y: 0.25 },
                    { x: 0.15, y: 0.28 },
                    { x: 0.18, y: 0.25 },
                    { x: 0.20, y: 0.30 },
                    { x: 0.17, y: 0.35 },
                    { x: 0.14, y: 0.32 },
                    { x: 0.11, y: 0.30 }
                ]
            },
            {
                name: "天琴座",
                page: 3,
                points: [
                    { x: 0.78, y: 0.15 },
                    { x: 0.80, y: 0.20 },
                    { x: 0.82, y: 0.18 },
                    { x: 0.84, y: 0.25 },
                    { x: 0.81, y: 0.30 }
                ]
            },
            {
                name: "飞马座",
                page: 3,
                points: [
                    { x: 0.45, y: 0.50 },
                    { x: 0.50, y: 0.48 },
                    { x: 0.55, y: 0.52 },
                    { x: 0.52, y: 0.58 },
                    { x: 0.48, y: 0.55 }
                ]
            }
        ];
        this.mouseX = 0;
        this.mouseY = 0;
        
        this.init();
    }

    init() {
        this.setupCanvas();
        this.createStars(200);
        this.createShootingStarTimer();
        this.setupEventListeners();
        this.animate();
    }

    setupCanvas() {
        const updateCanvasSize = () => {
            this.canvas.width = window.innerWidth;
            this.canvas.height = document.body.scrollHeight;
        };
        
        updateCanvasSize();
        window.addEventListener('resize', updateCanvasSize);
        // 页面内容变化时也更新canvas大小
        window.addEventListener('scroll', () => {
            if (this.canvas.height !== document.body.scrollHeight) {
                updateCanvasSize();
            }
        });
    }

    createStars(count) {
        this.stars = [];
        for (let i = 0; i < count; i++) {
            this.stars.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                size: Math.random() < 0.6 ? 1 : Math.random() < 0.9 ? 2 : 3,
                opacity: 0.3 + Math.random() * 0.7,
                twinkleSpeed: 0.01 + Math.random() * 0.02,
                twinklePhase: Math.random() * Math.PI * 2,
                baseOpacity: 0.3 + Math.random() * 0.4,
                breatheSpeed: 0.005 + Math.random() * 0.01,
                breathePhase: Math.random() * Math.PI * 2,
                breatheAmplitude: 0.2 + Math.random() * 0.3,
                isNearMouse: false
            });
        }
    }

    createShootingStarTimer() {
        setInterval(() => {
            if (Math.random() < 0.25) {
                this.createShootingStar();
            }
        }, 2500);
    }

    createShootingStar() {
        const viewportHeight = window.innerHeight;
        const scrollY = window.scrollY;

        const startX = -200 - Math.random() * 100;
        const startY = scrollY + Math.random() * (viewportHeight * 0.2);
        const endX = this.canvas.width + 200 + Math.random() * 100;
        const endY = scrollY + viewportHeight * 0.8 + Math.random() * viewportHeight * 0.3;
        
        const duration = 4000 + Math.random() * 3000; // 4-7秒
        
        const colors = [
            'rgba(255, 255, 255, 1)',
            'rgba(255, 248, 220, 1)',
            'rgba(173, 216, 230, 1)',
            'rgba(255, 182, 193, 1)'
        ];
        
        this.shootingStars.push({
            startX,
            startY,
            endX,
            endY,
            currentX: startX,
            currentY: startY,
            progress: 0,
            duration,
            startTime: Date.now(),
            length: 60 + Math.random() * 120,
            color: colors[Math.floor(Math.random() * colors.length)],
            opacity: 0,
            fadeInDuration: 300,
            fadeOutDuration: 2000 + Math.random() * 1000
        });
    }

    drawStars() {
        this.stars.forEach(star => {
            // 更新闪烁效果
            star.twinklePhase += star.twinkleSpeed;
            const twinkleOpacity = star.baseOpacity + Math.sin(star.twinklePhase) * 0.3;

            // 更新呼吸效果
            star.breathePhase += star.breatheSpeed;
            const breatheScale = 1 + Math.sin(star.breathePhase) * star.breatheAmplitude;
            const breatheOpacity = 0.8 + Math.sin(star.breathePhase * 0.7) * 0.2;

            // 鼠标交互效果
            const distance = Math.sqrt(
                Math.pow(this.mouseX - star.x, 2) +
                Math.pow(this.mouseY - star.y, 2)
            );

            let finalOpacity = twinkleOpacity * breatheOpacity;
            let finalSize = star.size * breatheScale;
            
            if (distance < 100) {
                const factor = 1 - (distance / 100);
                finalOpacity = Math.min(1, twinkleOpacity + factor * 0.7);
                finalSize = star.size * (1 + factor * 0.5);
            }
            
            this.ctx.save();
            this.ctx.globalAlpha = finalOpacity;
            this.ctx.fillStyle = 'white';
            
            if (star.size >= 3) {
                // 大星星添加光晕效果
                this.ctx.shadowColor = 'rgba(255, 255, 255, 0.8)';
                this.ctx.shadowBlur = 6;
            }
            
            this.ctx.beginPath();
            this.ctx.arc(star.x, star.y, finalSize / 2, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();
        });
    }

    drawShootingStars() {
        const currentTime = Date.now();
        const scrollY = window.scrollY;
        const viewportHeight = window.innerHeight;

        this.shootingStars = this.shootingStars.filter(star => {
            const elapsed = currentTime - star.startTime;
            const progress = elapsed / star.duration;

            if (progress >= 1) return false; // 移除完成的流星

            // 计算位置
            star.currentX = star.startX + (star.endX - star.startX) * progress;
            star.currentY = star.startY + (star.endY - star.startY) * progress;

            // 只绘制在当前视口附近的流星
            const starViewportY = star.currentY - scrollY;
            if (starViewportY < -200 || starViewportY > viewportHeight + 200) {
                return true; // 保留流星但不绘制
            }

            // 计算透明度
            const fadeInEnd = star.fadeInDuration;
            const fadeOutStart = star.duration - star.fadeOutDuration;

            if (elapsed < fadeInEnd) {
                star.opacity = elapsed / fadeInEnd;
            } else if (elapsed > fadeOutStart) {
                star.opacity = (star.duration - elapsed) / star.fadeOutDuration;
            } else {
                star.opacity = 1;
            }

            // 绘制流星
            this.drawShootingStar(star);

            return true;
        });
    }

    drawShootingStar(star) {
        const angle = Math.atan2(star.endY - star.startY, star.endX - star.startX);
        
        this.ctx.save();
        this.ctx.globalAlpha = star.opacity;
        
        // 绘制主体
        this.ctx.translate(star.currentX, star.currentY);
        this.ctx.rotate(angle);
        
        // 创建渐变
        const gradient = this.ctx.createLinearGradient(-star.length, 0, 0, 0);
        gradient.addColorStop(0, 'rgba(255, 255, 255, 0)');
        gradient.addColorStop(0.3, 'rgba(255, 255, 255, 0.3)');
        gradient.addColorStop(1, star.color);
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(-star.length, -1, star.length, 2);
        
        // 添加光晕效果
        this.ctx.shadowColor = 'rgba(255, 255, 255, 0.8)';
        this.ctx.shadowBlur = 8;
        this.ctx.fillRect(-star.length * 0.3, -0.5, star.length * 0.3, 1);
        
        this.ctx.restore();
    }

    drawConstellation() {
        const scrollY = window.scrollY;
        const viewportHeight = window.innerHeight;

        this.ctx.save();

        // 绘制所有星座，根据其在视口中的位置计算透明度
        this.constellations.forEach(constellation => {
            // 计算星座所在页面的起始位置
            const pageStartY = (constellation.page - 1) * viewportHeight;

            // 计算星座页面相对于当前滚动位置的进度
            const pageProgress = (scrollY - pageStartY) / viewportHeight;

            // 只绘制在视口附近的星座（当前页面和相邻页面）
            if (pageProgress < -0.3 || pageProgress > 1.3) return;

            // 根据页面滚动进度调整透明度（淡入淡出效果）
            let alpha = 0;
            if (pageProgress < -0.2) {
                // 页面还未进入视口，淡入
                alpha = Math.max(0, 1 + (pageProgress + 0.2) / 0.1);
            } else if (pageProgress <= 0.8) {
                // 页面在视口中，完全可见
                alpha = 1;
            } else if (pageProgress <= 1.2) {
                // 页面即将离开视口，淡出
                alpha = Math.max(0, 1 - (pageProgress - 0.8) / 0.4);
            } else {
                // 页面已完全离开视口
                alpha = 0;
            }

            if (alpha <= 0) return;

            // 设置连线样式
            this.ctx.strokeStyle = `rgba(255, 255, 255, ${0.4 * alpha})`;
            this.ctx.lineWidth = 1.5;
            this.ctx.shadowColor = `rgba(255, 255, 255, ${0.6 * alpha})`;
            this.ctx.shadowBlur = 2;

            // 绘制连线
            this.ctx.beginPath();
            constellation.points.forEach((point, index) => {
                const x = point.x * this.canvas.width;
                // 计算星座在对应页面的绝对Y坐标
                const y = (constellation.page - 1) * viewportHeight + point.y * viewportHeight;

                if (index === 0) {
                    this.ctx.moveTo(x, y);
                } else {
                    this.ctx.lineTo(x, y);
                }
            });
            this.ctx.stroke();

            // 绘制星座点（星星）
            this.ctx.fillStyle = `rgba(255, 255, 255, ${alpha})`;
            this.ctx.shadowColor = `rgba(255, 255, 255, ${0.9 * alpha})`;
            this.ctx.shadowBlur = 8;

            constellation.points.forEach(point => {
                const x = point.x * this.canvas.width;
                // 计算星座在对应页面的绝对Y坐标
                const y = (constellation.page - 1) * viewportHeight + point.y * viewportHeight;

                // 添加呼吸效果
                const time = Date.now() * 0.002;
                const breathe = 1 + Math.sin(time + x * 0.01 + y * 0.01) * 0.3;
                const radius = 2.5 * breathe;

                this.ctx.globalAlpha = alpha;
                this.ctx.beginPath();
                this.ctx.arc(x, y, radius, 0, Math.PI * 2);
                this.ctx.fill();

                // 添加额外的光晕
                this.ctx.globalAlpha = 0.3 * alpha;
                this.ctx.beginPath();
                this.ctx.arc(x, y, radius * 2, 0, Math.PI * 2);
                this.ctx.fill();
            });
        });

        this.ctx.restore();
    }

    setupEventListeners() {
        // 鼠标移动
        document.addEventListener('mousemove', (e) => {
            this.mouseX = e.clientX;
            this.mouseY = e.clientY + window.pageYOffset;
        });

        // 空格键增加星星
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space') {
                e.preventDefault();
                this.createStars(50);
            }
        });

        // 滚动指示器 - 第一页
        const scrollIndicator = document.getElementById('scrollIndicator');
        if (scrollIndicator) {
            scrollIndicator.addEventListener('click', () => {
                const windowHeight = window.innerHeight;
                window.scrollTo({
                    top: windowHeight,
                    behavior: 'smooth'
                });
            });
        }

        // 滚动指示器 - 第三页（返回顶部）
        const scrollIndicatorThird = document.getElementById('scrollIndicatorThird');
        if (scrollIndicatorThird) {
            scrollIndicatorThird.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // 点击事件检查更新
        document.addEventListener('click', (e) => {
            if (e.target.id === 'scrollIndicator' || e.target.closest('#scrollIndicator') ||
                e.target.id === 'scrollIndicatorThird' || e.target.closest('#scrollIndicatorThird')) return;
            
            for (let i = 0; i < 3; i++) {
                setTimeout(() => {
                    this.createShootingStar();
                }, i * 200);
            }
        });

        // 窗口大小变化时重新创建星星
        window.addEventListener('resize', () => {
            this.createStars(200);
        });
    }

    animate() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        this.drawStars();
        this.drawShootingStars();
        this.drawConstellation();
        
        requestAnimationFrame(() => this.animate());
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new CanvasStarryNight();
}); 