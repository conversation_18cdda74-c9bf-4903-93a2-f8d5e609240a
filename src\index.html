<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星空背景</title>
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <canvas id="starCanvas"></canvas>

    <div class="content">
        <div>
            <h1 class="title">CURSOR</h1>
            <p class="subtitle">Built to make you extraordinarily productive, Cursor is the best way to code with AI.</p>
            <div class="scroll-indicator" id="scrollIndicator">
                <div class="scroll-arrow">
                    <span>探索更多</span>
                    <div class="arrow-down">
                        <div class="arrow-line"></div>
                        <div class="arrow-head"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="content second-page">
        <div>
            <h2 class="section-title">探索更多功能</h2>
            <p class="section-text">这里是第二个页面内容，展示滚动功能的效果。</p>
            <p class="section-text">您可以使用鼠标滚轮或下方的按钮来浏览不同的页面内容。</p>
        </div>
    </div>

    <div class="content third-page">
        <div>
            <h2 class="section-title">无限可能</h2>
            <p class="section-text">在这个星空下，每一行代码都承载着创造的力量。</p>
            <p class="section-text">让我们一起探索编程的无限可能，创造属于未来的作品。</p>
            <div class="scroll-indicator" id="scrollIndicatorThird">
                <div class="scroll-arrow">
                    <span>返回顶部</span>
                    <div class="arrow-up">
                        <div class="arrow-line"></div>
                        <div class="arrow-head-up"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>

</html>